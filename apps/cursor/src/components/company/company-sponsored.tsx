export function CompanySponsored() {
  return (
    <a
      href="https://coderabbit.link/9dDbBj2"
      target="_blank"
      rel="noopener noreferrer"
      className="absolute bottom-0 right-4 bg-[#0D0D0D] p-2 flex flex-col gap-2"
    >
      <span className="text-xs text-[#666666] font-mono">Sponsored by</span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={110}
        height={18}
        fill="none"
      >
        <g clipPath="url(#a)">
          <mask
            id="b"
            width={110}
            height={18}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
              maskType: "luminance",
            }}
          >
            <path fill="#fff" d="M110 .237H0v17.434h110V.237Z" />
          </mask>
          <g mask="url(#b)">
            <path
              fill="#fff"
              d="M27.584 7.544a4.63 4.63 0 0 1 1.783-1.843c.762-.44 1.627-.66 2.592-.66 1.185 0 2.197.321 3.04.96.845.637 1.405 1.508 1.689 2.612h-2.67a2.203 2.203 0 0 0-.844-.972 2.329 2.329 0 0 0-1.245-.334c-.75 0-1.352.264-1.817.796-.465.532-.698 1.245-.698 2.134 0 .888.232 1.601.698 2.133.465.533 1.068.796 1.817.796.465 0 .879-.11 1.245-.334.366-.224.646-.545.844-.972h2.67c-.284 1.104-.849 1.97-1.688 2.604-.844.634-1.856.95-3.04.95-.966 0-1.831-.22-2.593-.66a4.635 4.635 0 0 1-1.783-1.834c-.427-.783-.638-1.676-.638-2.683 0-1.008.211-1.905.638-2.693ZM39.41 14.953a3.697 3.697 0 0 1-1.464-1.452c-.353-.63-.534-1.364-.534-2.204 0-.84.181-1.566.538-2.2a3.71 3.71 0 0 1 1.478-1.456c.624-.339 1.326-.506 2.101-.506.775 0 1.477.167 2.102.506a3.711 3.711 0 0 1 1.477 1.456c.362.634.538 1.368.538 2.2 0 .831-.18 1.566-.547 2.2a3.743 3.743 0 0 1-1.49 1.456c-.628.338-1.33.506-2.11.506-.78 0-1.473-.168-2.093-.506h.004Zm3.282-2.16c.327-.348.491-.845.491-1.496s-.16-1.144-.474-1.496a1.526 1.526 0 0 0-1.171-.523c-.465 0-.866.171-1.18.514-.314.344-.47.845-.47 1.5 0 .656.156 1.149.461 1.496.306.348.694.524 1.159.524a1.56 1.56 0 0 0 1.184-.524v.005ZM46.637 9.088c.305-.629.727-1.113 1.257-1.451a3.22 3.22 0 0 1 1.774-.506c.522 0 .995.11 1.426.334.43.225.77.524 1.016.898V4.606h2.43V15.34h-2.43v-1.162a2.504 2.504 0 0 1-.973.928c-.422.234-.913.348-1.469.348a3.19 3.19 0 0 1-1.774-.515c-.53-.343-.947-.831-1.257-1.464-.306-.634-.461-1.364-.461-2.2 0-.836.155-1.562.46-2.191v.004Zm4.97.74a1.626 1.626 0 0 0-1.228-.537c-.482 0-.891.176-1.227.528-.336.352-.504.84-.504 1.456s.168 1.108.504 1.474c.336.36.745.545 1.227.545.482 0 .892-.18 1.227-.537.336-.356.504-.844.504-1.464s-.168-1.11-.504-1.465ZM63.445 11.89H57.95c.038.502.198.89.473 1.153.28.264.625.4 1.03.4.607 0 1.029-.259 1.266-.782h2.584a3.596 3.596 0 0 1-.72 1.434c-.344.426-.779.76-1.3 1.003-.521.242-1.102.36-1.748.36-.776 0-1.469-.167-2.072-.505a3.566 3.566 0 0 1-1.421-1.452c-.34-.63-.513-1.364-.513-2.204 0-.84.168-1.575.504-2.204a3.519 3.519 0 0 1 1.413-1.452c.607-.339 1.3-.506 2.089-.506.788 0 1.447.163 2.045.493.595.33 1.064.796 1.4 1.408.336.607.504 1.32.504 2.133 0 .233-.013.475-.043.726l.004-.004Zm-2.442-1.376c0-.427-.142-.766-.426-1.016-.285-.251-.638-.379-1.064-.379-.426 0-.75.119-1.03.361-.279.242-.451.585-.516 1.03h3.04l-.004.004ZM69.708 15.344l-2.072-3.845h-.58v3.845h-2.43V5.16H68.7c.788 0 1.456.14 2.011.422.556.282.97.665 1.245 1.153.275.488.413 1.034.413 1.632a3.07 3.07 0 0 1-.56 1.812c-.374.533-.925.911-1.653 1.13l2.3 4.035h-2.748Zm-2.657-5.6h1.507c.443 0 .78-.11.999-.334.224-.225.336-.537.336-.942 0-.405-.112-.69-.336-.915-.224-.224-.556-.334-1-.334h-1.506v2.525ZM73.579 9.088c.306-.629.728-1.113 1.258-1.451.53-.34 1.123-.458 1.774-.458h4.87v8.16h-2.428v-1.143a2.643 2.643 0 0 1-.986.915c-.422.233-.913.347-1.469.347a3.17 3.17 0 0 1-1.761-.514c-.53-.343-.948-.832-1.258-1.465-.305-.634-.46-1.364-.46-2.2 0-.836.155-1.561.46-2.19Zm4.97.74a1.625 1.625 0 0 0-1.227-.537c-.483 0-.892.176-1.228.528-.336.352-.503.84-.503 1.456s.167 1.108.503 1.473c.336.361.745.546 1.228.546.482 0 .89-.18 1.227-.537.336-.356.504-.844.504-1.465 0-.62-.168-1.108-.504-1.465ZM86.37 7.478c.426-.233.913-.347 1.464-.347a3.22 3.22 0 0 1 1.775.506c.53.338.947.822 1.257 1.451.306.63.461 1.36.461 2.191 0 .832-.155 1.566-.46 2.2-.307.633-.729 1.122-1.258 1.465a3.19 3.19 0 0 1-1.775.514c-.56 0-1.046-.114-1.464-.338a2.604 2.604 0 0 1-.982-.906v1.13H82.96V4.606h2.43v3.787c.228-.378.555-.681.981-.915Zm1.981 2.345a1.65 1.65 0 0 0-1.245-.528c-.495 0-.89.18-1.227.537-.336.356-.504.845-.504 1.465 0 .62.168 1.108.504 1.465.336.356.745.537 1.227.537.483 0 .896-.18 1.237-.546.34-.36.512-.854.512-1.474s-.168-1.104-.504-1.456ZM95.728 7.478c.427-.233.913-.347 1.465-.347.654 0 1.244.167 1.773.506.53.339.948.822 1.258 1.451.306.63.461 1.36.461 2.191 0 .832-.155 1.566-.461 2.2-.305.633-.728 1.122-1.258 1.465a3.19 3.19 0 0 1-1.773.514c-.56 0-1.047-.114-1.465-.338a2.6 2.6 0 0 1-.981-.906v1.13h-2.43V4.606h2.43v3.787a2.53 2.53 0 0 1 .98-.915Zm1.981 2.345a1.65 1.65 0 0 0-1.244-.528c-.496 0-.892.18-1.228.537-.336.356-.504.845-.504 1.465 0 .62.168 1.108.504 1.465.336.356.745.537 1.228.537.482 0 .895-.18 1.235-.546.34-.36.513-.853.513-1.474 0-.62-.168-1.104-.504-1.456ZM101.856 6.022a1.25 1.25 0 0 1-.405-.95c0-.378.134-.708.405-.963.271-.256.616-.383 1.042-.383.426 0 .758.127 1.029.383.272.255.405.576.405.963s-.133.695-.405.95c-.271.255-.611.383-1.029.383-.418 0-.775-.128-1.042-.383Zm2.252 1.228v8.094h-2.429V7.245h2.429v.005ZM110.001 13.237v2.103h-1.236c-.879 0-1.568-.22-2.059-.66-.491-.44-.736-1.157-.736-2.156v-3.22h-.965V7.245h.965v-1.97h2.428v1.97h1.589v2.06h-1.589v3.25c0 .242.056.418.173.524.112.105.302.158.568.158h.866-.004Z"
            />
            <path
              fill="#FF570A"
              d="M12.704 17.671c-.938-.316-1.847-2.48-2.975-2.48 1.58.971.908 2.26.779 2.48h2.2-.004Z"
            />
            <path
              fill="#FF570A"
              d="M19.544 9.827C19.345 4.68 15.27.483 10.237.246 4.625-.014 0 4.553 0 10.223c0 2.165.672 4.166 1.817 5.798a3.835 3.835 0 0 0 2.804 1.632c-1.64-1.9-.245-3.488-.245-3.488-.65.321-.918 1.403-.918 1.403-1.231-.774-.947-1.698-.947-1.698.077-.193.254-.726 1.201-.532 1.835-6.172 7.877-2.956 7.877-2.956-.095-.194-.409-1.294-.409-1.294-3.923-.58-5.25-3.422-5.25-3.422 3.64-1.13 5.853 1.94 5.853 1.94-1.012-2.521-3.445-1.518-4.078-5.653 4.224.717 4.836 4.1 4.922 4.83.06-.14.396-.33 1.783-.246 2.149.127 3.859 2.358 3.859 2.358 0 4.522-5.41 3.431-5.41 6.85 0 .8.853.752 1.137 1.139.233.316.134.668.087.783h.512a3.776 3.776 0 0 0 3.084-1.575 10.118 10.118 0 0 0 1.865-6.27v.005Z"
            />
          </g>
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h110v18H0z" />
          </clipPath>
        </defs>
      </svg>
    </a>
  );
}
