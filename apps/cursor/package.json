{"name": "@directories/cursor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "email:dev": "email dev --port 3002 --dir src/emails/templates", "email:build": "email build", "email:export": "email export"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@directories/data": "workspace:*", "@hookform/resolvers": "^4.1.3", "@openpanel/nextjs": "^1.0.7", "@polar-sh/nextjs": "^0.3.22", "@polar-sh/sdk": "^0.30.0", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@react-email/components": "0.0.33", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.3", "@vercel/functions": "^2.0.0", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "geist": "^1.3.1", "lucide-react": "^0.469.0", "motion": "^12.4.2", "nanoid": "^5.1.3", "next": "15.3.1", "next-safe-action": "^7.10.4", "next-themes": "^0.4.4", "nuqs": "^2.4.1", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.54.2", "resend": "^4.1.2", "server-only": "^0.0.1", "slugify": "^1.6.6", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/wicg-file-system-access": "^2023.10.5", "postcss": "^8", "tailwindcss": "^3.4.17", "react-email": "3.0.7", "typescript": "^5"}}