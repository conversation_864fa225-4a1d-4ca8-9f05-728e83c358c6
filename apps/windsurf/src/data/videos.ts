export const videos = [{
    "title": "WTF is MCP? Build an MCP server with Windsurf and Mintlify in 30 seconds",
    "description": "Learn what MCP is and how to create your own one quickly using Windsurf and Mintlify MCP CLI",
    "url": "https://www.youtube.com/embed/H-zXNNpchlg",
    "author": {
      "name": "<PERSON><PERSON>",
      "image": "https://yt3.googleusercontent.com/bErjBPyFJCBYxgfuG4qi_I_0TKWmmw50A2NoMyhxaLch-lzMQlpfSLRpsGg9xhnj5B2xp471=s160-c-k-c0x00ffffff-no-rj"
    }
  },{
    "title": "Is Windsurf Editor Better Than Cursor AI?",
    "description": "The Windsurf Editor combines agent and copilot functionalities for collaborative coding. It offers deep contextual awareness, multi-file editing, and utilizes Cascade for real-time codebase understanding and command execution.",
    "url": "https://www.youtube.com/embed/ENKR0_dE7Wc",
    "author": {
      "name": "<PERSON>",
      "image": "https://yt3.ggpht.com/D-8FuN67wE8kacdi47DXPVEXytyKGO014dYwUaYV6Mho3MZMyUf47n19Jtv_U96COsBE-3cBgXM=s88-c-k-c0x00ffffff-no-rj"
    }
  },
  {
    "title": "AI Agent Tools for Beginners – A Complete MCP Guide",
    "description": "What MCP is, how it works and how to setup MCP servers with Windsurf.",
    "url": "https://www.youtube.com/embed/7NqQylSBIIo",
    "author": {
      "name": "Richardson Dackam",
      "image": "https://yt3.ggpht.com/t0k-MTFK3RinQ8TUo2uRKtkiDIk0z5EXIUP03kREVx6Fod0tJPjfXpSiPFSMkLjVjliDOlHa=s88-c-k-c0x00ffffff-no-rj"
    }
  },
  {
    "title": "Windsurf Tutorial for Beginners (AI Code Editor) - Better than Cursor??",
    "description": "The Windsurf Editor combines agent and copilot functionalities for collaborative coding. It offers deep contextual awareness, multi-file editing, and utilizes Cascade for real-time codebase understanding and command execution.",
    "url": "https://www.youtube.com/embed/8TcWGk1DJVs",
    "author": {
      "name": "Tech With Tim",
      "image": "https://yt3.ggpht.com/ytc/AIdro_k15oXl74WcnpdL-uVBv6IHBgAfXEEnpUiS-IoEyV1auyY=s88-c-k-c0x00ffffff-no-rj"
    }
  },
  {
    "title": "How To Use Windsurf Editor For Beginners",
    "description": "Flows: Integrated AI Collaboration Flows combine Copilots and Agents to work synchronously with the developer. This ensures that both the developer and AI operate on the same state for a cohesive coding experience.",
    "url": "https://www.youtube.com/embed/ZEqSuggBKo8",
    "author": {
      "name": "Corbin Brown",
      "image": "https://yt3.ggpht.com/D-8FuN67wE8kacdi47DXPVEXytyKGO014dYwUaYV6Mho3MZMyUf47n19Jtv_U96COsBE-3cBgXM=s88-c-k-c0x00ffffff-no-rj"
    }
  },
  {
    "title": "Windsurf IDE: NEW AI Editor - Cursor Alternative That's FREE & LOCAL!",
    "description": "In this video, we're diving into Windsurf IDE, a powerful, AI-driven editor designed as a free and local alternative to Cursor! 🌊 Windsurf takes AI-assisted coding to the next level with its proprietary context engine—no embeddings here! This makes it incredibly effective for complex, multi-file projects by providing deep code understanding and context-aware edits across entire codebases. Let's explore Windsurf's unique features, including multi-file editing, natural language commands with Control-I, and the Super Complete feature for intelligent, cursor-free suggestions.",
    "url": "https://www.youtube.com/embed/C_D8V9odBQ8",
    "author": {
      "name": "WorldofAI",
      "image": "https://yt3.ggpht.com/D-8FuN67wE8kacdi47DXPVEXytyKGO014dYwUaYV6Mho3MZMyUf47n19Jtv_U96COsBE-3cBgXM=s88-c-k-c0x00ffffff-no-rj"
    }
  },
  {
    "title": "Save Time and Flow Action Credits with Cascade Autogenerated Memories - Windsurf Editor",
    "description": "Learn how Cascade’s Autogenerated Memories helps you pick up right where you left off—without needing extra prompts or spending additional flow action credits on repeated tasks.",
    "url": "https://www.youtube.com/embed/DKUe0ST_qi4",
    "author": {
      "name": "Codeium - Windsurf",
      "image": "https://yt3.ggpht.com/VH_za2DotkucDgWAuOJnD0TWwyUagpa7yOoT32XXJuu-mVBHSldfldLfmSfWoDDwnuTpuZmMxw=s88-c-k-c0x00ffffff-no-rj"
    }
  },
  {
    "title": "Spend Less Time Approving with Automated Command - Windsurf Editor",
    "description": "Tired of manually approving every command? Cascade’s Automated Command lets you save time by running safe commands automatically while still asking for approval for higher-risk tasks.",
    "url": "https://www.youtube.com/embed/bdKZ_vGHh1o",
    "author": {
      "name": "Codeium - Windsurf",
      "image": "https://yt3.ggpht.com/VH_za2DotkucDgWAuOJnD0TWwyUagpa7yOoT32XXJuu-mVBHSldfldLfmSfWoDDwnuTpuZmMxw=s88-c-k-c0x00ffffff-no-rj"
    }
  }
];
