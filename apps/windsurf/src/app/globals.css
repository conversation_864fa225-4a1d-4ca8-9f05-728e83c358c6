@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0, 0%, 95%;
    --foreground: 210 40% 2%;
    --card: 0 0% 90%;
    --card-foreground: 210 40% 2%;
    --popover: 0, 0%, 95%;
    --popover-foreground: 210 40% 2%;
    --primary: 210 40% 2%;
    --primary-foreground: 222.2 47.4% 88.8%;
    --secondary: 217.2 32.6% 82.5%;
    --secondary-foreground: 210 40% 2%;
    --muted: 217.2 32.6% 82.5%;
    --muted-foreground: 215 20.2% 34.9%;
    --accent: 0 0% 83%;
    --accent-foreground: 210 40% 2%;
    --destructive: 0 62.8% 69.4%;
    --destructive-foreground: 210 40% 2%;
    --border: 0 0% 83%;
    --input: 217.2 32.6% 82.5%;
    --ring: 212.7 26.8% 16.1%;
    --chart-1: 40 70% 50%;
    --chart-2: 340 60% 55%;
    --chart-3: 210 80% 45%;
    --chart-4: 100 65% 40%;
    --chart-5: 160 75% 45%;
  }

  .dark {
    --background: 0, 0%, 5%;
    --foreground: 210 40% 98%;
    --card: 0 0% 10%;
    --card-foreground: 210 40% 98%;
    --popover: 0, 0%, 5%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 0 0% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 0 0% 17%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    font-family: var(--font-sans), system-ui, sans-serif;
    @apply bg-background text-foreground;
  }
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
