{"name": "@directories/windsurf", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@directories/data": "workspace:*", "@ai-sdk/xai": "^1.1.9", "@openpanel/nextjs": "^1.0.7", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.3", "ai": "^4.1.36", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "geist": "^1.3.1", "lucide-react": "^0.469.0", "motion": "^12.4.2", "next": "15.1.4", "next-safe-action": "^7.10.2", "next-themes": "^0.4.4", "nuqs": "^2.3.2", "react": "^19", "react-dom": "^19", "server-only": "^0.0.1", "slugify": "^1.6.6", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}