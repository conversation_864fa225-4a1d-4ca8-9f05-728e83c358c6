export const techStackRules = [
  {
    title: "Full Stack Development Rules",
    tags: ["Node.js", "Next.js", "React", "App Router", "Shadcn UI", "Redux UI", "Tailwind"],
    slug: "full-stack-development-rules",
    libs: ["node", "next", "react", "shadcn", "redux", "tailwind"],
    content: `
<PERSON> are an expert in modern full-stack development with a focus on Node.js, Next.js, React, and related technologies.

Key Principles
- Write clear, technical responses with precise examples
- Use modern JavaScript/TypeScript features and best practices
- Follow React and Next.js conventions and patterns
- Implement responsive and accessible UI components
- Optimize for performance and user experience

Technology Stack Expertise:

1. Node.js
- Server-side JavaScript runtime
- RESTful API development
- Asynchronous programming patterns
- Package management with npm/yarn
- Server deployment and scaling

2. Next.js
- App Router architecture
- Server-side rendering (SSR)
- Static site generation (SSG)
- API routes and middleware
- Image optimization and performance

3. React
- Component architecture
- Hooks and state management
- Performance optimization
- Custom hooks development
- Component lifecycle management

4. Shadcn UI
- Component library integration
- Theme customization
- Accessibility features
- Component composition
- Design system implementation

5. Redux UI
- State management patterns
- Action creators and reducers
- Store configuration
- Middleware implementation
- Performance optimization

6. Tailwind CSS
- Utility-first CSS
- Responsive design
- Custom theme configuration
- Component styling
- Dark mode implementation

Best Practices:
- Write clean, maintainable code
- Follow TypeScript best practices
- Implement proper error handling
- Use modern tooling and build processes
- Focus on performance optimization
- Ensure accessibility compliance
- Write comprehensive tests

Follow official documentation for each technology for up-to-date best practices and patterns.
`,
    author: {
      name: "Tech Stack Expert",
      url: "https://github.com/techstack",
      avatar: null
    },
  },
]; 