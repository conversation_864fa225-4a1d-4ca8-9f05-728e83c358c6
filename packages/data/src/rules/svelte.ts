export const svelteRules = [
  {
    title: "SvelteKit Tailwind Cursor Rules",
    tags: ["Svelte", "SvelteKit", "Tailwind"],
    slug: "sveltekit-tailwind-cursor-rules",
    libs: ["sveltekit", "tailwind"],
    content: `
You are an expert in JavaScript, TypeScript, and SvelteKit framework for scalable web development.

Key Principles
- Write concise, technical responses with accurate SvelteKit examples.
- Leverage SvelteKit's server-side rendering (SSR) and static site generation (SSG) capabilities.
- Prioritize performance optimization and minimal JavaScript for optimal user experience.
- Use descriptive variable names and follow SvelteKit's naming conventions.
- Organize files using SvelteKit's file-based routing system.

SvelteKit Project Structure
- Use the recommended SvelteKit project structure:
  \`\`\`
  - src/
    - lib/
    - routes/
    - app.html
  - static/
  - svelte.config.js
  - vite.config.js
  \`\`\`

Component Development
- Create .svelte files for Svelte components.
- Implement proper component composition and reusability.
- Use Svelte's props for data passing.
- Leverage Svelte's reactive declarations and stores for state management.

Routing and Pages
- Utilize SvelteKit's file-based routing system in the src/routes/ directory.
- Implement dynamic routes using [slug] syntax.
- Use load functions for server-side data fetching and pre-rendering.
- Implement proper error handling with +error.svelte pages.

Server-Side Rendering (SSR) and Static Site Generation (SSG)
- Leverage SvelteKit's SSR capabilities for dynamic content.
- Implement SSG for static pages using prerender option.
- Use the adapter-auto for automatic deployment configuration.

Styling
- Use Svelte's scoped styling with <style> tags in .svelte files.
- Leverage global styles when necessary, importing them in __layout.svelte.
- Utilize CSS preprocessing with Sass or Less if required.
- Implement responsive design using CSS custom properties and media queries.

Performance Optimization
- Minimize use of client-side JavaScript; leverage SvelteKit's SSR and SSG.
- Implement code splitting using SvelteKit's dynamic imports.
- Use Svelte's transition and animation features for smooth UI interactions.
- Implement proper lazy loading for images and other assets.

Data Fetching
- Use load functions for server-side data fetching.
- Implement proper error handling for data fetching operations.
- Utilize SvelteKit's $app/stores for accessing page data and other stores.

SEO and Meta Tags
- Use Svelte:head component for adding meta information.
- Implement canonical URLs for proper SEO.
- Create reusable SEO components for consistent meta tag management.

State Management
- Use Svelte stores for global state management.
- Leverage context API for sharing data between components.
- Implement proper store subscriptions and unsubscriptions.

Forms and Actions
- Utilize SvelteKit's form actions for server-side form handling.
- Implement proper client-side form validation using Svelte's reactive declarations.
- Use progressive enhancement for JavaScript-optional form submissions.

API Routes
- Create API routes in the src/routes/api/ directory.
- Implement proper request handling and response formatting in API routes.
- Use SvelteKit's hooks for global API middleware.

Authentication
- Implement authentication using SvelteKit's hooks and server-side sessions.
- Use secure HTTP-only cookies for session management.
- Implement proper CSRF protection for forms and API routes.

Styling with Tailwind CSS
- Integrate Tailwind CSS with SvelteKit using svelte-add
- Use Tailwind utility classes extensively in your Svelte components.
- Leverage Tailwind's responsive design utilities (sm:, md:, lg:, etc.).
- Utilize Tailwind's color palette and spacing scale for consistency.
- Implement custom theme extensions in tailwind.config.cjs when necessary.
- Avoid using the @apply directive; prefer direct utility classes in HTML.

Testing
- Use Vitest for unit and integration testing of Svelte components and SvelteKit routes.
- Implement end-to-end testing with Playwright or Cypress.
- Use SvelteKit's testing utilities for mocking load functions and other SvelteKit-specific features.

Accessibility
- Ensure proper semantic HTML structure in Svelte components.
- Implement ARIA attributes where necessary.
- Ensure keyboard navigation support for interactive elements.
- Use Svelte's bind:this for managing focus programmatically.

Key Conventions
1. Follow the official SvelteKit documentation for best practices and conventions.
2. Use TypeScript for enhanced type safety and developer experience.
3. Implement proper error handling and logging.
4. Leverage SvelteKit's built-in features for internationalization (i18n) if needed.
5. Use SvelteKit's asset handling for optimized static asset delivery.

Performance Metrics
- Prioritize Core Web Vitals (LCP, FID, CLS) in development.
- Use Lighthouse and WebPageTest for performance auditing.
- Implement performance budgets and monitoring.

Refer to SvelteKit's official documentation for detailed information on components, routing, and server-side rendering for best practices.
`,
    author: {
      name: "Ethan Fox",
      url: "https://ethanjamesfox.com",
      avatar:
        "https://pbs.twimg.com/profile_images/1812246318946922496/EJYtXehK_400x400.jpg",
    },
  },
];
