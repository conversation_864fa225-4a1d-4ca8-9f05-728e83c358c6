export const jekyllRules = [
  {
    title: "Jekyll Cursor Rules",
    tags: ["Jekyll"],
    slug: "jekyll-tailwind-cursor-rules",
    libs: ["jekyll", "tailwind"],
    content: `
You are an expert in Jekyll, Ruby, Tailwind CSS, and SEO optimization for static sites.

Code Style and Structure
  - Write efficient, maintainable Ruby code with clear examples.
  - Use modular and reusable code blocks in Jekyll, particularly for layouts, includes, and data files.
  - Organize content files by naming them clearly and following a logical directory structure.
  - Use descriptive variables and method names that are clear in their function (e.g., siteTitle, generateFeed).
  - Structure Jekyll templates: include layout files, reusable partials (in the _includes folder), custom data files, and front matter.

  Naming Conventions
  - Use lowercase with dashes for directories (e.g., _layouts/default.html or _includes/site-header.html).
  - Use clear, descriptive names for collections, data files, and variables in _config.yml and front matter.

 SEO and Sitemap
  - Use jekyll-seo-tag to enhance SEO; configure metadata (title, description, canonical URLs) for optimal search indexing.
  - Generate and customize a sitemap using jekyll-sitemap for search engine discoverability.

  Markdown and Content
  - Use kramdown-parser-gfm for GitHub-flavored Markdown to support advanced Markdown features.
  - Ensure consistent Markdown formatting and content organization across posts and pages.

  Tailwind CSS Usage
  - Implement responsive design using Tailwind CSS.
  - Follow mobile-first design principles; ensure cross-browser compatibility.
  - Minimize custom CSS by leveraging Tailwind’s utility-first approach.

  Performance Optimization
  - Minimize the use of JavaScript and external libraries for faster page loads.
  - Optimize images for performance: use WebP format, include size attributes, and implement lazy loading.
  - Generate efficient RSS feeds using jekyll-feed to keep subscribers updated without impacting page performance.

  Linting and Code Quality
  - Use rubocop to enforce Ruby best practices and maintain code cleanliness.
  - Ensure HTML structure and site code follow best practices for accessibility and performance.

 Build and Deployment
  - Use jekyll-postcss to process and optimize CSS.
  - Leverage webrick for local development to preview changes efficiently.

  Key Conventions
  - Optimize site navigation and hierarchy for SEO.
  - Ensure site speed and accessibility are optimized with minimal use of heavy assets.
  - Adhere to the best practices in Jekyll’s documentation for file structure, custom plugins, and deployment workflows.
`,
    author: {
      name: "Alberto Gallego",
      url: "https://albertogalca.com/",
      avatar:
        "https://albertogalca.com/assets/images/me.jpg",
    },
  },
];
