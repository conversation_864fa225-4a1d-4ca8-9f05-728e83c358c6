class SnakeGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gridSize = 20;
        this.tileCount = this.canvas.width / this.gridSize;
        
        // 游戏状态
        this.gameRunning = false;
        this.gamePaused = false;
        this.gameSpeed = 150;
        
        // 玩家蛇
        this.playerSnake = {
            x: 10,
            y: 10,
            dx: 0,
            dy: 0,
            tail: [],
            score: 0,
            color: '#4299e1'
        };
        
        // AI蛇
        this.aiSnake = {
            x: 30,
            y: 30,
            dx: 0,
            dy: 0,
            tail: [],
            score: 0,
            color: '#f56565'
        };
        
        // 食物
        this.foods = [];
        this.maxFoods = 3;
        
        // AI设置
        this.aiDifficulty = 'medium';
        this.aiMoveCounter = 0;
        
        this.initializeGame();
        this.setupEventListeners();
    }
    
    initializeGame() {
        this.resetSnakes();
        this.generateFoods();
        this.updateUI();
        this.draw();
    }
    
    resetSnakes() {
        // 重置玩家蛇
        this.playerSnake.x = 10;
        this.playerSnake.y = 10;
        this.playerSnake.dx = 0;
        this.playerSnake.dy = 0;
        this.playerSnake.tail = [];
        this.playerSnake.score = 0;
        
        // 重置AI蛇
        this.aiSnake.x = 30;
        this.aiSnake.y = 30;
        this.aiSnake.dx = 0;
        this.aiSnake.dy = 0;
        this.aiSnake.tail = [];
        this.aiSnake.score = 0;
    }
    
    setupEventListeners() {
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!this.gameRunning || this.gamePaused) return;
            
            switch(e.key.toLowerCase()) {
                case 'w':
                case 'arrowup':
                    if (this.playerSnake.dy === 0) {
                        this.playerSnake.dx = 0;
                        this.playerSnake.dy = -1;
                    }
                    break;
                case 's':
                case 'arrowdown':
                    if (this.playerSnake.dy === 0) {
                        this.playerSnake.dx = 0;
                        this.playerSnake.dy = 1;
                    }
                    break;
                case 'a':
                case 'arrowleft':
                    if (this.playerSnake.dx === 0) {
                        this.playerSnake.dx = -1;
                        this.playerSnake.dy = 0;
                    }
                    break;
                case 'd':
                case 'arrowright':
                    if (this.playerSnake.dx === 0) {
                        this.playerSnake.dx = 1;
                        this.playerSnake.dy = 0;
                    }
                    break;
            }
        });
        
        // 按钮事件
        document.getElementById('startBtn').addEventListener('click', () => this.startGame());
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());
        document.getElementById('difficulty').addEventListener('change', (e) => {
            this.aiDifficulty = e.target.value;
        });
    }
    
    startGame() {
        this.gameRunning = true;
        this.gamePaused = false;
        document.getElementById('startBtn').disabled = true;
        document.getElementById('pauseBtn').disabled = false;
        document.getElementById('gameStatus').textContent = '游戏进行中';
        
        // 给蛇初始方向
        this.playerSnake.dx = 1;
        this.playerSnake.dy = 0;
        this.aiSnake.dx = -1;
        this.aiSnake.dy = 0;
        
        this.gameLoop();
    }
    
    togglePause() {
        this.gamePaused = !this.gamePaused;
        document.getElementById('pauseBtn').textContent = this.gamePaused ? '继续' : '暂停';
        document.getElementById('gameStatus').textContent = this.gamePaused ? '游戏暂停' : '游戏进行中';
        
        if (!this.gamePaused) {
            this.gameLoop();
        }
    }
    
    resetGame() {
        this.gameRunning = false;
        this.gamePaused = false;
        document.getElementById('startBtn').disabled = false;
        document.getElementById('pauseBtn').disabled = true;
        document.getElementById('pauseBtn').textContent = '暂停';
        document.getElementById('gameStatus').textContent = '准备开始';
        
        this.resetSnakes();
        this.generateFoods();
        this.updateUI();
        this.draw();
    }
    
    gameLoop() {
        if (!this.gameRunning || this.gamePaused) return;
        
        this.updateGame();
        this.draw();
        
        setTimeout(() => this.gameLoop(), this.gameSpeed);
    }
    
    updateGame() {
        // 更新玩家蛇
        this.updateSnake(this.playerSnake);
        
        // 更新AI蛇
        this.updateAI();
        this.updateSnake(this.aiSnake);
        
        // 检查碰撞
        this.checkCollisions();
        
        // 检查食物
        this.checkFoodCollision();
        
        // 更新UI
        this.updateUI();
    }
    
    updateSnake(snake) {
        // 移动蛇头
        snake.x += snake.dx;
        snake.y += snake.dy;
        
        // 边界处理（穿墙）
        if (snake.x < 0) snake.x = this.tileCount - 1;
        if (snake.x >= this.tileCount) snake.x = 0;
        if (snake.y < 0) snake.y = this.tileCount - 1;
        if (snake.y >= this.tileCount) snake.y = 0;
        
        // 更新蛇身
        snake.tail.push({x: snake.x, y: snake.y});
        
        // 保持蛇的长度
        while (snake.tail.length > snake.score + 1) {
            snake.tail.shift();
        }
    }
    
    updateAI() {
        this.aiMoveCounter++;
        
        // 根据难度调整AI反应速度
        let moveFrequency;
        switch(this.aiDifficulty) {
            case 'easy': moveFrequency = 3; break;
            case 'medium': moveFrequency = 2; break;
            case 'hard': moveFrequency = 1; break;
        }
        
        if (this.aiMoveCounter % moveFrequency !== 0) return;
        
        // 寻找最近的食物
        let nearestFood = this.findNearestFood(this.aiSnake);
        if (!nearestFood) return;
        
        // 计算到食物的方向
        let dx = nearestFood.x - this.aiSnake.x;
        let dy = nearestFood.y - this.aiSnake.y;
        
        // 可能的移动方向
        let possibleMoves = [
            {dx: 0, dy: -1}, // 上
            {dx: 0, dy: 1},  // 下
            {dx: -1, dy: 0}, // 左
            {dx: 1, dy: 0}   // 右
        ];
        
        // 过滤掉反向移动
        possibleMoves = possibleMoves.filter(move => 
            !(move.dx === -this.aiSnake.dx && move.dy === -this.aiSnake.dy)
        );
        
        // 根据难度选择移动策略
        let bestMove;
        if (this.aiDifficulty === 'easy') {
            // 简单模式：随机移动，偶尔朝食物移动
            if (Math.random() < 0.7) {
                bestMove = this.getBestMoveToFood(dx, dy, possibleMoves);
            } else {
                bestMove = possibleMoves[Math.floor(Math.random() * possibleMoves.length)];
            }
        } else {
            // 中等和困难模式：智能寻路
            bestMove = this.getBestMoveToFood(dx, dy, possibleMoves);
            
            // 困难模式：避免碰撞检测
            if (this.aiDifficulty === 'hard') {
                bestMove = this.avoidCollision(bestMove, possibleMoves);
            }
        }
        
        if (bestMove) {
            this.aiSnake.dx = bestMove.dx;
            this.aiSnake.dy = bestMove.dy;
        }
    }
    
    getBestMoveToFood(dx, dy, possibleMoves) {
        // 优先选择能接近食物的方向
        let bestMove = possibleMoves[0];
        let bestScore = Infinity;
        
        for (let move of possibleMoves) {
            let score = 0;
            
            // 计算移动后到食物的距离
            if (dx > 0 && move.dx > 0) score -= 2;
            if (dx < 0 && move.dx < 0) score -= 2;
            if (dy > 0 && move.dy > 0) score -= 2;
            if (dy < 0 && move.dy < 0) score -= 2;
            
            if (score < bestScore) {
                bestScore = score;
                bestMove = move;
            }
        }
        
        return bestMove;
    }
    
    avoidCollision(preferredMove, possibleMoves) {
        // 检查首选移动是否会导致碰撞
        let nextX = this.aiSnake.x + preferredMove.dx;
        let nextY = this.aiSnake.y + preferredMove.dy;
        
        // 边界处理
        if (nextX < 0) nextX = this.tileCount - 1;
        if (nextX >= this.tileCount) nextX = 0;
        if (nextY < 0) nextY = this.tileCount - 1;
        if (nextY >= this.tileCount) nextY = 0;
        
        // 检查是否会撞到自己
        for (let segment of this.aiSnake.tail) {
            if (segment.x === nextX && segment.y === nextY) {
                // 寻找安全的替代移动
                for (let move of possibleMoves) {
                    let testX = this.aiSnake.x + move.dx;
                    let testY = this.aiSnake.y + move.dy;
                    
                    if (testX < 0) testX = this.tileCount - 1;
                    if (testX >= this.tileCount) testX = 0;
                    if (testY < 0) testY = this.tileCount - 1;
                    if (testY >= this.tileCount) testY = 0;
                    
                    let safe = true;
                    for (let segment of this.aiSnake.tail) {
                        if (segment.x === testX && segment.y === testY) {
                            safe = false;
                            break;
                        }
                    }
                    
                    if (safe) return move;
                }
            }
        }
        
        return preferredMove;
    }
    
    findNearestFood(snake) {
        let nearest = null;
        let minDistance = Infinity;
        
        for (let food of this.foods) {
            let distance = Math.abs(food.x - snake.x) + Math.abs(food.y - snake.y);
            if (distance < minDistance) {
                minDistance = distance;
                nearest = food;
            }
        }
        
        return nearest;
    }
    
    checkCollisions() {
        // 检查玩家蛇自撞
        for (let i = 0; i < this.playerSnake.tail.length - 1; i++) {
            if (this.playerSnake.tail[i].x === this.playerSnake.x && 
                this.playerSnake.tail[i].y === this.playerSnake.y) {
                this.gameOver('AI获胜！玩家撞到自己');
                return;
            }
        }
        
        // 检查AI蛇自撞
        for (let i = 0; i < this.aiSnake.tail.length - 1; i++) {
            if (this.aiSnake.tail[i].x === this.aiSnake.x && 
                this.aiSnake.tail[i].y === this.aiSnake.y) {
                this.gameOver('玩家获胜！AI撞到自己');
                return;
            }
        }
        
        // 检查两蛇相撞
        if (this.playerSnake.x === this.aiSnake.x && this.playerSnake.y === this.aiSnake.y) {
            if (this.playerSnake.score > this.aiSnake.score) {
                this.gameOver('玩家获胜！');
            } else if (this.aiSnake.score > this.playerSnake.score) {
                this.gameOver('AI获胜！');
            } else {
                this.gameOver('平局！');
            }
            return;
        }
        
        // 检查玩家蛇撞AI蛇身
        for (let segment of this.aiSnake.tail) {
            if (segment.x === this.playerSnake.x && segment.y === this.playerSnake.y) {
                this.gameOver('AI获胜！玩家撞到AI');
                return;
            }
        }
        
        // 检查AI蛇撞玩家蛇身
        for (let segment of this.playerSnake.tail) {
            if (segment.x === this.aiSnake.x && segment.y === this.aiSnake.y) {
                this.gameOver('玩家获胜！AI撞到玩家');
                return;
            }
        }
    }
    
    checkFoodCollision() {
        // 检查玩家蛇吃食物
        for (let i = this.foods.length - 1; i >= 0; i--) {
            let food = this.foods[i];
            if (food.x === this.playerSnake.x && food.y === this.playerSnake.y) {
                this.playerSnake.score++;
                this.foods.splice(i, 1);
                this.generateFood();
            }
        }
        
        // 检查AI蛇吃食物
        for (let i = this.foods.length - 1; i >= 0; i--) {
            let food = this.foods[i];
            if (food.x === this.aiSnake.x && food.y === this.aiSnake.y) {
                this.aiSnake.score++;
                this.foods.splice(i, 1);
                this.generateFood();
            }
        }
    }
    
    generateFoods() {
        this.foods = [];
        for (let i = 0; i < this.maxFoods; i++) {
            this.generateFood();
        }
    }
    
    generateFood() {
        let food;
        let attempts = 0;
        
        do {
            food = {
                x: Math.floor(Math.random() * this.tileCount),
                y: Math.floor(Math.random() * this.tileCount)
            };
            attempts++;
        } while (this.isFoodOnSnake(food) && attempts < 100);
        
        this.foods.push(food);
    }
    
    isFoodOnSnake(food) {
        // 检查食物是否在玩家蛇上
        if (food.x === this.playerSnake.x && food.y === this.playerSnake.y) return true;
        for (let segment of this.playerSnake.tail) {
            if (segment.x === food.x && segment.y === food.y) return true;
        }
        
        // 检查食物是否在AI蛇上
        if (food.x === this.aiSnake.x && food.y === this.aiSnake.y) return true;
        for (let segment of this.aiSnake.tail) {
            if (segment.x === food.x && segment.y === food.y) return true;
        }
        
        return false;
    }
    
    gameOver(message) {
        this.gameRunning = false;
        document.getElementById('gameStatus').textContent = message;
        document.getElementById('startBtn').disabled = false;
        document.getElementById('pauseBtn').disabled = true;
    }
    
    updateUI() {
        document.getElementById('playerScore').textContent = this.playerSnake.score;
        document.getElementById('aiScore').textContent = this.aiSnake.score;
    }
    
    draw() {
        // 清空画布
        this.ctx.fillStyle = '#1a202c';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制网格
        this.drawGrid();
        
        // 绘制食物
        this.drawFoods();
        
        // 绘制蛇
        this.drawSnake(this.playerSnake);
        this.drawSnake(this.aiSnake);
    }
    
    drawGrid() {
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 1;
        
        for (let i = 0; i <= this.tileCount; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(i * this.gridSize, 0);
            this.ctx.lineTo(i * this.gridSize, this.canvas.height);
            this.ctx.stroke();
            
            this.ctx.beginPath();
            this.ctx.moveTo(0, i * this.gridSize);
            this.ctx.lineTo(this.canvas.width, i * this.gridSize);
            this.ctx.stroke();
        }
    }
    
    drawFoods() {
        this.ctx.fillStyle = '#ffd700';
        for (let food of this.foods) {
            this.ctx.fillRect(
                food.x * this.gridSize + 2,
                food.y * this.gridSize + 2,
                this.gridSize - 4,
                this.gridSize - 4
            );
        }
    }
    
    drawSnake(snake) {
        this.ctx.fillStyle = snake.color;
        
        // 绘制蛇身
        for (let segment of snake.tail) {
            this.ctx.fillRect(
                segment.x * this.gridSize + 1,
                segment.y * this.gridSize + 1,
                this.gridSize - 2,
                this.gridSize - 2
            );
        }
        
        // 绘制蛇头（稍大一些）
        this.ctx.fillStyle = snake === this.playerSnake ? '#2b6cb0' : '#c53030';
        this.ctx.fillRect(
            snake.x * this.gridSize,
            snake.y * this.gridSize,
            this.gridSize,
            this.gridSize
        );
    }
}

// 初始化游戏
window.addEventListener('load', () => {
    new SnakeGame();
});
