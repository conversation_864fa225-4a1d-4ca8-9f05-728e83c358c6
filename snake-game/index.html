<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇人机对战</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>贪吃蛇人机对战</h1>
        
        <div class="game-info">
            <div class="score-board">
                <div class="player-score">
                    <h3>玩家</h3>
                    <div class="score" id="playerScore">0</div>
                </div>
                <div class="ai-score">
                    <h3>AI</h3>
                    <div class="score" id="aiScore">0</div>
                </div>
            </div>
            
            <div class="game-status">
                <div id="gameStatus">准备开始</div>
                <button id="startBtn">开始游戏</button>
                <button id="pauseBtn" disabled>暂停</button>
                <button id="resetBtn">重置</button>
            </div>
        </div>

        <div class="game-area">
            <canvas id="gameCanvas" width="800" height="600"></canvas>
        </div>

        <div class="controls">
            <h3>控制说明</h3>
            <p>使用 WASD 或方向键控制你的蛇</p>
            <p>W/↑: 上, S/↓: 下, A/←: 左, D/→: 右</p>
        </div>

        <div class="difficulty">
            <label for="difficulty">AI难度:</label>
            <select id="difficulty">
                <option value="easy">简单</option>
                <option value="medium" selected>中等</option>
                <option value="hard">困难</option>
            </select>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
