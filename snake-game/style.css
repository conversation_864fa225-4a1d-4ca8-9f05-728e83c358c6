* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 900px;
    width: 100%;
}

h1 {
    color: #4a5568;
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 20px;
}

.score-board {
    display: flex;
    gap: 40px;
}

.player-score, .ai-score {
    text-align: center;
}

.player-score h3 {
    color: #2d3748;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.ai-score h3 {
    color: #e53e3e;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.score {
    font-size: 2em;
    font-weight: bold;
    padding: 10px 20px;
    border-radius: 10px;
    min-width: 80px;
}

.player-score .score {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
}

.ai-score .score {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
}

.game-status {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}

#gameStatus {
    font-size: 1.2em;
    font-weight: bold;
    color: #4a5568;
    margin-bottom: 10px;
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 5px;
}

#startBtn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

#startBtn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
}

#pauseBtn {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
}

#pauseBtn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(237, 137, 54, 0.4);
}

#resetBtn {
    background: linear-gradient(135deg, #a0aec0, #718096);
    color: white;
}

#resetBtn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(160, 174, 192, 0.4);
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.game-area {
    margin: 20px 0;
    display: flex;
    justify-content: center;
}

#gameCanvas {
    border: 3px solid #4a5568;
    border-radius: 10px;
    background: #1a202c;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.controls {
    margin: 20px 0;
    padding: 20px;
    background: rgba(74, 85, 104, 0.1);
    border-radius: 10px;
}

.controls h3 {
    color: #4a5568;
    margin-bottom: 10px;
}

.controls p {
    color: #718096;
    margin: 5px 0;
}

.difficulty {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.difficulty label {
    font-weight: bold;
    color: #4a5568;
}

.difficulty select {
    padding: 8px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1em;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.difficulty select:focus {
    outline: none;
    border-color: #4299e1;
}

@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 10px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    .game-info {
        flex-direction: column;
        gap: 15px;
    }
    
    .score-board {
        gap: 20px;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 400px;
        height: auto;
    }
    
    button {
        padding: 8px 16px;
        font-size: 0.9em;
    }
}
